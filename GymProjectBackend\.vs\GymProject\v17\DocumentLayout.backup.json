{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmembershipdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmembershipdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\memberworkoutprogram.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\memberworkoutprogram.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\transaction.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\transaction.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\product.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\product.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\licensetransaction.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\licensetransaction.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\licensepackage.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\licensepackage.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\expense.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\expense.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\systemexercise.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\systemexercise.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\town.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\town.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efexpensedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efexpensedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\usercompany.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\usercompany.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\remainingdebt.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\remainingdebt.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\payment.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\payment.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\membershiptype.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\membershiptype.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\membershipfreezehistory.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\membershipfreezehistory.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\membership.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\membership.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\member.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\member.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\exercisecategory.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\exercisecategory.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\entryexithistory.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\entryexithistory.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\debtpayment.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\debtpayment.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\companyuser.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\companyuser.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\companyexercise.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\companyexercise.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\companyadress.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\companyadress.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\company.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\company.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\city.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\city.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efuserdevicedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efuserdevicedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\expensescontroller.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\expensescontroller.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\expensemanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\expensemanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membershiptypemanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membershiptypemanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\membervalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\membervalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\productmanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\productmanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\transactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\transactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\abstract\\itransactionservice.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\abstract\\itransactionservice.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\core\\dataaccess\\entityframework\\efentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|solutionrelative:core\\dataaccess\\entityframework\\efentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\core\\dataaccess\\entityframework\\efcompanyentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|solutionrelative:core\\dataaccess\\entityframework\\efcompanyentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\workoutprogramtemplatevalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\workoutprogramtemplatevalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\uservalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\uservalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\companyuservalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\companyuservalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\companyvalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\companyvalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\expensevalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\expensevalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\membershiptypevalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\membershiptypevalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\companyadressvalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\companyadressvalidator.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\membercontroller.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\membercontroller.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efremainingdebtdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efremainingdebtdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efsystemexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efsystemexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efunifiedcompanydal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efunifiedcompanydal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcompanyadressdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcompanyadressdal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcitydal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcitydal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\workoutprogramtemplatemanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\workoutprogramtemplatemanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\package-lock.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\package-lock.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\properties\\publishprofiles\\folderprofile.pubxml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\properties\\publishprofiles\\folderprofile.pubxml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\qrcodeencryptionmanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\qrcodeencryptionmanager.cs||{A6C744A8-0E4A-4FC6-886A-***********4}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 149, "SelectedChildIndex": 9, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "Transaction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Transaction.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Transaction.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Transaction.cs", "RelativeToolTip": "Entities\\Concrete\\Transaction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:17.715Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Town.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Town.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Town.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Town.cs", "RelativeToolTip": "Entities\\Concrete\\Town.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:13.874Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "SystemExercise.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\SystemExercise.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\SystemExercise.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\SystemExercise.cs", "RelativeToolTip": "Entities\\Concrete\\SystemExercise.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:11.503Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "EfExpenseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAAADYAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T21:39:04.642Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "RemainingDebt.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\RemainingDebt.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\RemainingDebt.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\RemainingDebt.cs", "RelativeToolTip": "Entities\\Concrete\\RemainingDebt.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:08.992Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Product.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Product.cs", "RelativeToolTip": "Entities\\Concrete\\Product.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:04.673Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Expense.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Expense.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Expense.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Expense.cs", "RelativeToolTip": "Entities\\Concrete\\Expense.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABEAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:17.041Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MemberWorkoutProgram.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MemberWorkoutProgram.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\MemberWorkoutProgram.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MemberWorkoutProgram.cs", "RelativeToolTip": "Entities\\Concrete\\MemberWorkoutProgram.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:54.31Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "EfMemberDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ViewState": "AgIAAN4BAAAAAAAAAAAAAPgBAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:42.761Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "EfMembershipDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "ViewState": "AgIAAGQAAAAAAAAAAAAkwIYAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T15:36:01.774Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "UserCompany.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\UserCompany.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\UserCompany.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\UserCompany.cs", "RelativeToolTip": "Entities\\Concrete\\UserCompany.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:22.516Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "EfMemberWorkoutProgramDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ViewState": "AgIAABkAAAAAAAAAAAAAADIAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T13:19:21.762Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "Payment.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Payment.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Payment.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Payment.cs", "RelativeToolTip": "Entities\\Concrete\\Payment.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:02.024Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "LicensePackage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicensePackage.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\LicensePackage.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicensePackage.cs", "RelativeToolTip": "Entities\\Concrete\\LicensePackage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:24.761Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "LicenseTransaction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicenseTransaction.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\LicenseTransaction.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicenseTransaction.cs", "RelativeToolTip": "Entities\\Concrete\\LicenseTransaction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:29.867Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "MembershipType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipType.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\MembershipType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipType.cs", "RelativeToolTip": "Entities\\Concrete\\MembershipType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:50.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "MembershipFreezeHistory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipFreezeHistory.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\MembershipFreezeHistory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipFreezeHistory.cs", "RelativeToolTip": "Entities\\Concrete\\MembershipFreezeHistory.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:43.511Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Membership.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Membership.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Membership.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Membership.cs", "RelativeToolTip": "Entities\\Concrete\\Membership.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:38.431Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "Member.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Member.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Member.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Member.cs", "RelativeToolTip": "Entities\\Concrete\\Member.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:34.07Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "ExerciseCategory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\ExerciseCategory.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\ExerciseCategory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\ExerciseCategory.cs", "RelativeToolTip": "Entities\\Concrete\\ExerciseCategory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:14.329Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "EntryExitHistory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\EntryExitHistory.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\EntryExitHistory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\EntryExitHistory.cs", "RelativeToolTip": "Entities\\Concrete\\EntryExitHistory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:12.745Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "DebtPayment.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\DebtPayment.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\DebtPayment.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\DebtPayment.cs", "RelativeToolTip": "Entities\\Concrete\\DebtPayment.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:08.497Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "CompanyUser.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyUser.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\CompanyUser.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyUser.cs", "RelativeToolTip": "Entities\\Concrete\\CompanyUser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:06.907Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "CompanyExercise.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyExercise.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\CompanyExercise.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyExercise.cs", "RelativeToolTip": "Entities\\Concrete\\CompanyExercise.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:03.559Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "CompanyAdress.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyAdress.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\CompanyAdress.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyAdress.cs", "RelativeToolTip": "Entities\\Concrete\\CompanyAdress.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:55:01.264Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "Company.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Company.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Company.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Company.cs", "RelativeToolTip": "Entities\\Concrete\\Company.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:54:54.629Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "City.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\City.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\City.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\City.cs", "RelativeToolTip": "Entities\\Concrete\\City.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:54:51.936Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "MembershipTypeManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipTypeManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MembershipTypeManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipTypeManager.cs", "RelativeToolTip": "Business\\Concrete\\MembershipTypeManager.cs", "ViewState": "AgIAACAAAAAAAAAAAAAuwD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T17:11:31.064Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "CompanyValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\CompanyValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\CompanyValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:54:56.554Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "ExpenseValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\ExpenseValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\ExpenseValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\ExpenseValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\ExpenseValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:54:54.793Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "MembershipTypeValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\MembershipTypeValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\MembershipTypeValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\MembershipTypeValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\MembershipTypeValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:54:52.584Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "MemberValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\MemberValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\MemberValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\MemberValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\MemberValidator.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAAAAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:54:51.942Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "MemberManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeToolTip": "Business\\Concrete\\MemberManager.cs", "ViewState": "AgIAALwAAAAAAAAAAAAYwNAAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:32.286Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "ExpenseManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ExpenseManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\ExpenseManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ExpenseManager.cs", "RelativeToolTip": "Business\\Concrete\\ExpenseManager.cs", "ViewState": "AgIAAGgAAAAAAAAAAAAvwHUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T21:39:26.296Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "EfUserDeviceDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-12T11:51:57.664Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "ExpensesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\ExpensesController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\ExpensesController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\ExpensesController.cs", "RelativeToolTip": "WebAPI\\Controllers\\ExpensesController.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAuwHsAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T21:39:28.583Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "EfCompanyEntityRepositoryBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "RelativeDocumentMoniker": "Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "RelativeToolTip": "Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "ViewState": "AgIAACcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T16:51:59.834Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "ITransactionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Abstract\\ITransactionService.cs", "RelativeDocumentMoniker": "Business\\Abstract\\ITransactionService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Abstract\\ITransactionService.cs", "RelativeToolTip": "Business\\Abstract\\ITransactionService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T15:58:51.333Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "ProductManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ProductManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\ProductManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ProductManager.cs", "RelativeToolTip": "Business\\Concrete\\ProductManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAC8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T16:29:26.545Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "TransactionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TransactionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\TransactionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TransactionManager.cs", "RelativeToolTip": "Business\\Concrete\\TransactionManager.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAAAHYAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T15:58:54.82Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "EfEntityRepositoryBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "RelativeDocumentMoniker": "Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "RelativeToolTip": "Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAAAHUAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T16:54:14.559Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "WorkoutProgramTemplateValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\WorkoutProgramTemplateValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\WorkoutProgramTemplateValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\WorkoutProgramTemplateValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\WorkoutProgramTemplateValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:55:12.916Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "UserValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\UserValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\UserValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\UserValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\UserValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:55:13.686Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "CompanyUserValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyUserValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\CompanyUserValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyUserValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\CompanyUserValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:54:03.339Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "CompanyAdressValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAC4AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:08:25.429Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "MemberController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\MemberController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberController.cs", "RelativeToolTip": "WebAPI\\Controllers\\MemberController.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAkwG0AAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:14.155Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "EfRemainingDebtDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "ViewState": "AgIAAGYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T22:46:23.127Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "EfSystemExerciseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T22:46:22.348Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "EfUnifiedCompanyDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T22:46:13.666Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "WorkoutProgramTemplateManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\WorkoutProgramTemplateManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\WorkoutProgramTemplateManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\WorkoutProgramTemplateManager.cs", "RelativeToolTip": "Business\\Concrete\\WorkoutProgramTemplateManager.cs", "ViewState": "AgIAADIAAAAAAAAAAAArwEYAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:17:55.375Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "FolderProfile.pubxml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "RelativeDocumentMoniker": "WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "RelativeToolTip": "WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-07-05T22:21:54.369Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeDocumentMoniker": "WebAPI\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeToolTip": "WebAPI\\Program.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T22:21:39.746Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "package-lock.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\package-lock.json", "RelativeDocumentMoniker": "WebAPI\\package-lock.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\package-lock.json", "RelativeToolTip": "WebAPI\\package-lock.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:21:38.404Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.Development.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.Development.json", "RelativeToolTip": "WebAPI\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:21:30.189Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "WebAPI\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\launchSettings.json", "RelativeToolTip": "WebAPI\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:21:26.96Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "EfCompanyAdressDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "ViewState": "AgIAABwAAAAAAAAAAAAYwCgAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T20:48:10.328Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "EfWorkoutProgramTemplateDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAABICAAB/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:17:55.961Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "EfCityDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T20:48:07.421Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "QrCodeEncryptionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\QrCodeEncryptionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\QrCodeEncryptionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\QrCodeEncryptionManager.cs", "RelativeToolTip": "Business\\Concrete\\QrCodeEncryptionManager.cs", "ViewState": "AgIAAN4AAAAAAAAAAAAAAO4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T17:33:22.708Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeToolTip": "WebAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:22:06.161Z"}]}]}]}